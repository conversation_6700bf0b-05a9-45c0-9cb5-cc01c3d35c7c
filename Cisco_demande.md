
**Toujours vérifier ta mémoire et vérifier ensuite ce fichier. On ne sait jamais, je peux en cours de route te mettre de nouvelles instructions lorsque toi tu travailles.pas de fichier MD à la racine. Le seul fichier MD qui existe, c'est le mien : `cisco_demande.md`** Ça, c'est mon fichier personnel. Tu n'écris rien du tout dedans. Tu ne rédiges absolument rien du tout. C'est mon fichier à moi, celui-là. 



archiveService.ts:175 🔍 Calcul des statistiques d'archives pour userId: nTfdEgyg0gP4GK2TnAOKNnI6flZ2
archiveService.ts:119 🔍 Récupération des archives pour userId: nTfdEgyg0gP4GK2TnAOKNnI6flZ2
archiveService.ts:127 🔍 Exécution de la requête archives...
archiveService.ts:143 ❌ Erreur lors de la récupération des archives: Missing or insufficient permissions.
overrideMethod @ hook.js:608
getUserArchives @ archiveService.ts:143
await in getUserArchives
(anonymous) @ useArchive.ts:27
(anonymous) @ useArchive.ts:140
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10016
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10054
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10031
doubleInvokeEffectsOnFiber @ react-dom_client.js?v=7f10f1b8:11461
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1487
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11442
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
commitDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11469
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11309
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:145 ❌ Stack trace: FirebaseError: Missing or insufficient permissions.
overrideMethod @ hook.js:608
getUserArchives @ archiveService.ts:145
await in getUserArchives
(anonymous) @ useArchive.ts:27
(anonymous) @ useArchive.ts:140
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10016
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10054
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10031
doubleInvokeEffectsOnFiber @ react-dom_client.js?v=7f10f1b8:11461
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1487
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11442
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
commitDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11469
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11309
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:175 🔍 Calcul des statistiques d'archives pour userId: nTfdEgyg0gP4GK2TnAOKNnI6flZ2
archiveService.ts:119 🔍 Récupération des archives pour userId: nTfdEgyg0gP4GK2TnAOKNnI6flZ2
archiveService.ts:127 🔍 Exécution de la requête archives...
archiveService.ts:165 ❌ Erreur lors de la récupération de l'archive: Missing or insufficient permissions.
overrideMethod @ hook.js:608
getArchive @ archiveService.ts:165
await in getArchive
checkAutoArchive @ archiveService.ts:225
(anonymous) @ useArchive.ts:89
(anonymous) @ useArchive.ts:147
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9960
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9940
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9899
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11302
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:228 🔄 Déclenchement de l'archivage automatique pour 2024
archiveService.ts:47 🗄️ Début de l'archivage des données 2024 pour l'utilisateur nTfdEgyg0gP4GK2TnAOKNnI6flZ2
archiveService.ts:165 ❌ Erreur lors de la récupération de l'archive: Missing or insufficient permissions.
overrideMethod @ hook.js:608
getArchive @ archiveService.ts:165
await in getArchive
checkAutoArchive @ archiveService.ts:225
(anonymous) @ useArchive.ts:89
(anonymous) @ App.tsx:129
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9960
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9940
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9899
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11302
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:228 🔄 Déclenchement de l'archivage automatique pour 2024
archiveService.ts:47 🗄️ Début de l'archivage des données 2024 pour l'utilisateur nTfdEgyg0gP4GK2TnAOKNnI6flZ2
archiveService.ts:165 ❌ Erreur lors de la récupération de l'archive: Missing or insufficient permissions.
overrideMethod @ hook.js:608
getArchive @ archiveService.ts:165
await in getArchive
checkAutoArchive @ archiveService.ts:225
(anonymous) @ useArchive.ts:89
(anonymous) @ useArchive.ts:147
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10016
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10054
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10031
doubleInvokeEffectsOnFiber @ react-dom_client.js?v=7f10f1b8:11461
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1487
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11442
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
commitDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11469
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11309
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:228 🔄 Déclenchement de l'archivage automatique pour 2024
archiveService.ts:47 🗄️ Début de l'archivage des données 2024 pour l'utilisateur nTfdEgyg0gP4GK2TnAOKNnI6flZ2
archiveService.ts:165 ❌ Erreur lors de la récupération de l'archive: Missing or insufficient permissions.
overrideMethod @ hook.js:608
getArchive @ archiveService.ts:165
await in getArchive
checkAutoArchive @ archiveService.ts:225
(anonymous) @ useArchive.ts:89
(anonymous) @ App.tsx:129
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10016
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10054
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10031
doubleInvokeEffectsOnFiber @ react-dom_client.js?v=7f10f1b8:11461
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1487
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11442
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
commitDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11469
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11309
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:228 🔄 Déclenchement de l'archivage automatique pour 2024
archiveService.ts:47 🗄️ Début de l'archivage des données 2024 pour l'utilisateur nTfdEgyg0gP4GK2TnAOKNnI6flZ2
archiveService.ts:143 ❌ Erreur lors de la récupération des archives: Missing or insufficient permissions.
overrideMethod @ hook.js:608
getUserArchives @ archiveService.ts:143
await in getUserArchives
getArchiveStats @ archiveService.ts:176
(anonymous) @ useArchive.ts:30
await in (anonymous)
(anonymous) @ useArchive.ts:140
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9960
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9940
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9899
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11302
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:145 ❌ Stack trace: FirebaseError: Missing or insufficient permissions.
overrideMethod @ hook.js:608
getUserArchives @ archiveService.ts:145
await in getUserArchives
getArchiveStats @ archiveService.ts:176
(anonymous) @ useArchive.ts:30
await in (anonymous)
(anonymous) @ useArchive.ts:140
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9960
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9940
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9899
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11302
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:178 🔍 Archives pour statistiques: 0
archiveService.ts:181 🔍 Aucune archive trouvée, retour des stats par défaut
archiveService.ts:143 ❌ Erreur lors de la récupération des archives: Missing or insufficient permissions.
overrideMethod @ hook.js:608
getUserArchives @ archiveService.ts:143
await in getUserArchives
getArchiveStats @ archiveService.ts:176
(anonymous) @ useArchive.ts:30
await in (anonymous)
(anonymous) @ useArchive.ts:140
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10016
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10054
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10031
doubleInvokeEffectsOnFiber @ react-dom_client.js?v=7f10f1b8:11461
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1487
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11442
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
commitDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11469
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11309
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:145 ❌ Stack trace: FirebaseError: Missing or insufficient permissions.
overrideMethod @ hook.js:608
getUserArchives @ archiveService.ts:145
await in getUserArchives
getArchiveStats @ archiveService.ts:176
(anonymous) @ useArchive.ts:30
await in (anonymous)
(anonymous) @ useArchive.ts:140
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10016
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10054
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10031
doubleInvokeEffectsOnFiber @ react-dom_client.js?v=7f10f1b8:11461
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1487
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11442
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
commitDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11469
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11309
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:178 🔍 Archives pour statistiques: 0
archiveService.ts:181 🔍 Aucune archive trouvée, retour des stats par défaut
notificationService.ts:226 [2025-07-25T22:31:28.196Z]  @firebase/firestore: Firestore (11.10.0): Uncaught Error in snapshot listener: FirebaseError: [code=failed-precondition]: The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/florasynth-a461d/firestore/indexes?create_composite=Clpwcm9qZWN0cy9mbG9yYXN5bnRoLWE0NjFkL2RhdGFiYXNlcy8oZGVmYXVsdCkvY29sbGVjdGlvbkdyb3Vwcy9kaWFnbm9zdGljX2V2ZW50cy9pbmRleGVzL18QARoNCgljb21wbGV0ZWQQARoSCg5uZXh0QWN0aW9uRGF0ZRABGgwKCF9fbmFtZV9fEAE
overrideMethod @ hook.js:608
defaultLogHandler @ chunk-PY7TWBGT.js?v=7f10f1b8:1275
error @ chunk-PY7TWBGT.js?v=7f10f1b8:1343
__PRIVATE_logError @ firebase_firestore.js?v=7f10f1b8:2626
error @ firebase_firestore.js?v=7f10f1b8:15079
onError @ firebase_firestore.js?v=7f10f1b8:13904
__PRIVATE_eventManagerOnWatchError @ firebase_firestore.js?v=7f10f1b8:13857
__PRIVATE_removeAndCleanupTarget @ firebase_firestore.js?v=7f10f1b8:14574
(anonymous) @ firebase_firestore.js?v=7f10f1b8:14511
Promise.then
__PRIVATE_syncEngineRejectListen @ firebase_firestore.js?v=7f10f1b8:14511
__PRIVATE_handleTargetError @ firebase_firestore.js?v=7f10f1b8:13356
__PRIVATE_onWatchStreamChange @ firebase_firestore.js?v=7f10f1b8:13357
onNext @ firebase_firestore.js?v=7f10f1b8:13015
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12974
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12974
a_ @ firebase_firestore.js?v=7f10f1b8:12587
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12727
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12690
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Z2.ta @ firebase_firestore.js?v=7f10f1b8:2540
Rb @ firebase_firestore.js?v=7f10f1b8:1419
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1208
fd @ firebase_firestore.js?v=7f10f1b8:2341
h.Fa @ firebase_firestore.js?v=7f10f1b8:2308
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
ec @ firebase_firestore.js?v=7f10f1b8:2294
Rb @ firebase_firestore.js?v=7f10f1b8:1416
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1203
Hb @ firebase_firestore.js?v=7f10f1b8:1178
h.Ga @ firebase_firestore.js?v=7f10f1b8:2228
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
fc @ firebase_firestore.js?v=7f10f1b8:2172
h.connect @ firebase_firestore.js?v=7f10f1b8:2132
Y2.m @ firebase_firestore.js?v=7f10f1b8:2488
Ho @ firebase_firestore.js?v=7f10f1b8:12684
send @ firebase_firestore.js?v=7f10f1b8:12575
k_ @ firebase_firestore.js?v=7f10f1b8:12892
H_ @ firebase_firestore.js?v=7f10f1b8:13044
__PRIVATE_sendWatchRequest @ firebase_firestore.js?v=7f10f1b8:13300
(anonymous) @ firebase_firestore.js?v=7f10f1b8:13329
__PRIVATE_onWatchStreamOpen @ firebase_firestore.js?v=7f10f1b8:13328
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
o_ @ firebase_firestore.js?v=7f10f1b8:12581
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12732
setTimeout
P_ @ firebase_firestore.js?v=7f10f1b8:12731
z_ @ firebase_firestore.js?v=7f10f1b8:13003
W_ @ firebase_firestore.js?v=7f10f1b8:12967
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12957
Promise.then
auth @ firebase_firestore.js?v=7f10f1b8:12953
start @ firebase_firestore.js?v=7f10f1b8:12852
__PRIVATE_startWatchStream @ firebase_firestore.js?v=7f10f1b8:13310
__PRIVATE_remoteStoreListen @ firebase_firestore.js?v=7f10f1b8:13282
__PRIVATE_allocateTargetAndMaybeListen @ firebase_firestore.js?v=7f10f1b8:14345
await in __PRIVATE_allocateTargetAndMaybeListen
__PRIVATE_syncEngineListen @ firebase_firestore.js?v=7f10f1b8:14323
__PRIVATE_eventManagerListen @ firebase_firestore.js?v=7f10f1b8:13790
(anonymous) @ firebase_firestore.js?v=7f10f1b8:17989
await in (anonymous)
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
__PRIVATE_firestoreClientListen @ firebase_firestore.js?v=7f10f1b8:17989
onSnapshot @ firebase_firestore.js?v=7f10f1b8:17992
getUserNotifications @ notificationService.ts:226
(anonymous) @ useNotifications.ts:105
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9960
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9940
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9899
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11302
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<NotificationBadge>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
Header @ App.tsx:98
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopConcurrentByScheduler @ react-dom_client.js?v=7f10f1b8:10864
renderRootConcurrent @ react-dom_client.js?v=7f10f1b8:10844
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<Header>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
ProtectedLayout @ App.tsx:143
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopConcurrentByScheduler @ react-dom_client.js?v=7f10f1b8:10864
renderRootConcurrent @ react-dom_client.js?v=7f10f1b8:10844
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
notificationService.ts:996 Erreur lors de la génération des notifications préventives: FirebaseError: The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/florasynth-a461d/firestore/indexes?create_composite=Cltwcm9qZWN0cy9mbG9yYXN5bnRoLWE0NjFkL2RhdGFiYXNlcy8oZGVmYXVsdCkvY29sbGVjdGlvbkdyb3Vwcy9kaWFnbm9zdGljX3JlY29yZHMvaW5kZXhlcy9fEAEaCwoHcGxhbnRJZBABGg0KCXRpbWVzdGFtcBACGgwKCF9fbmFtZV9fEAI
overrideMethod @ hook.js:608
generatePreventiveNotifications @ notificationService.ts:996
await in generatePreventiveNotifications
(anonymous) @ useNotifications.ts:372
(anonymous) @ useNotifications.ts:384
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9960
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9940
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9899
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11302
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
notificationService.ts:996 Erreur lors de la génération des notifications préventives: FirebaseError: The query requires an index. You can create it here: https://console.firebase.google.com/v1/r/project/florasynth-a461d/firestore/indexes?create_composite=Cltwcm9qZWN0cy9mbG9yYXN5bnRoLWE0NjFkL2RhdGFiYXNlcy8oZGVmYXVsdCkvY29sbGVjdGlvbkdyb3Vwcy9kaWFnbm9zdGljX3JlY29yZHMvaW5kZXhlcy9fEAEaCwoHcGxhbnRJZBABGg0KCXRpbWVzdGFtcBACGgwKCF9fbmFtZV9fEAI
overrideMethod @ hook.js:608
generatePreventiveNotifications @ notificationService.ts:996
await in generatePreventiveNotifications
(anonymous) @ useNotifications.ts:372
(anonymous) @ useNotifications.ts:384
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10016
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10054
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10031
doubleInvokeEffectsOnFiber @ react-dom_client.js?v=7f10f1b8:11461
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1487
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11442
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
commitDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11469
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11309
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:128  POST https://firestore.googleapis.com/google.firestore.v1.Firestore/Listen/channel?VER=8&database=projects%2Fflorasynth-a461d%2Fdatabases%2F(default)&gsessionid=IqIe3shoMNo87rwxVjY80wJC4FbH2sNVbA4RRed3iXs&SID=43x0zKReTcStQc0vFXImgg&RID=65848&AID=37&zx=je5agj6l7ern&t=1 400 (Bad Request)
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1203
Hb @ firebase_firestore.js?v=7f10f1b8:1178
ed @ firebase_firestore.js?v=7f10f1b8:2248
h.Ga @ firebase_firestore.js?v=7f10f1b8:2231
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
fc @ firebase_firestore.js?v=7f10f1b8:2172
Ub @ firebase_firestore.js?v=7f10f1b8:2365
M2.Y @ firebase_firestore.js?v=7f10f1b8:1296
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
Mc @ firebase_firestore.js?v=7f10f1b8:1831
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1203
Hb @ firebase_firestore.js?v=7f10f1b8:1178
ed @ firebase_firestore.js?v=7f10f1b8:2248
h.Ga @ firebase_firestore.js?v=7f10f1b8:2231
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
fc @ firebase_firestore.js?v=7f10f1b8:2172
Ub @ firebase_firestore.js?v=7f10f1b8:2365
M2.Y @ firebase_firestore.js?v=7f10f1b8:1296
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
Mc @ firebase_firestore.js?v=7f10f1b8:1831
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1203
Hb @ firebase_firestore.js?v=7f10f1b8:1178
ed @ firebase_firestore.js?v=7f10f1b8:2248
h.Ga @ firebase_firestore.js?v=7f10f1b8:2231
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
fc @ firebase_firestore.js?v=7f10f1b8:2172
Ub @ firebase_firestore.js?v=7f10f1b8:2365
M2.Y @ firebase_firestore.js?v=7f10f1b8:1296
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
Mc @ firebase_firestore.js?v=7f10f1b8:1831
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1203
Hb @ firebase_firestore.js?v=7f10f1b8:1178
ed @ firebase_firestore.js?v=7f10f1b8:2248
h.Ga @ firebase_firestore.js?v=7f10f1b8:2231
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
fc @ firebase_firestore.js?v=7f10f1b8:2172
Y2.o @ firebase_firestore.js?v=7f10f1b8:2501
Ho @ firebase_firestore.js?v=7f10f1b8:12684
send @ firebase_firestore.js?v=7f10f1b8:12575
k_ @ firebase_firestore.js?v=7f10f1b8:12892
H_ @ firebase_firestore.js?v=7f10f1b8:13044
__PRIVATE_sendWatchRequest @ firebase_firestore.js?v=7f10f1b8:13300
__PRIVATE_remoteStoreListen @ firebase_firestore.js?v=7f10f1b8:13283
__PRIVATE_allocateTargetAndMaybeListen @ firebase_firestore.js?v=7f10f1b8:14345
await in __PRIVATE_allocateTargetAndMaybeListen
__PRIVATE_syncEngineListen @ firebase_firestore.js?v=7f10f1b8:14323
__PRIVATE_eventManagerListen @ firebase_firestore.js?v=7f10f1b8:13790
__PRIVATE_executeQueryViaSnapshotListener @ firebase_firestore.js?v=7f10f1b8:15600
(anonymous) @ firebase_firestore.js?v=7f10f1b8:15601
await in (anonymous)
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
__PRIVATE_firestoreClientGetDocumentsViaSnapshotListener @ firebase_firestore.js?v=7f10f1b8:15590
getDocs @ firebase_firestore.js?v=7f10f1b8:17916
getUserArchives @ archiveService.ts:128
getArchiveStats @ archiveService.ts:176
(anonymous) @ useArchive.ts:30
await in (anonymous)
(anonymous) @ useArchive.ts:140
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9960
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9940
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9899
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11302
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
notificationService.ts:226  POST https://firestore.googleapis.com/google.firestore.v1.Firestore/Listen/channel?VER=8&database=projects%2Fflorasynth-a461d%2Fdatabases%2F(default)&gsessionid=IqIe3shoMNo87rwxVjY80wJC4FbH2sNVbA4RRed3iXs&SID=43x0zKReTcStQc0vFXImgg&RID=65849&TYPE=terminate&zx=1oxuzzq69w8a 400 (Bad Request)
gc @ firebase_firestore.js?v=7f10f1b8:2147
Y2.close @ firebase_firestore.js?v=7f10f1b8:2491
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12726
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12690
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Z2.ta @ firebase_firestore.js?v=7f10f1b8:2540
Rb @ firebase_firestore.js?v=7f10f1b8:1419
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1208
fd @ firebase_firestore.js?v=7f10f1b8:2341
h.Fa @ firebase_firestore.js?v=7f10f1b8:2308
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
ec @ firebase_firestore.js?v=7f10f1b8:2294
Rb @ firebase_firestore.js?v=7f10f1b8:1416
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1203
Hb @ firebase_firestore.js?v=7f10f1b8:1178
h.Ga @ firebase_firestore.js?v=7f10f1b8:2228
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
fc @ firebase_firestore.js?v=7f10f1b8:2172
h.connect @ firebase_firestore.js?v=7f10f1b8:2132
Y2.m @ firebase_firestore.js?v=7f10f1b8:2488
Ho @ firebase_firestore.js?v=7f10f1b8:12684
send @ firebase_firestore.js?v=7f10f1b8:12575
k_ @ firebase_firestore.js?v=7f10f1b8:12892
H_ @ firebase_firestore.js?v=7f10f1b8:13044
__PRIVATE_sendWatchRequest @ firebase_firestore.js?v=7f10f1b8:13300
(anonymous) @ firebase_firestore.js?v=7f10f1b8:13329
__PRIVATE_onWatchStreamOpen @ firebase_firestore.js?v=7f10f1b8:13328
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
o_ @ firebase_firestore.js?v=7f10f1b8:12581
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12732
setTimeout
P_ @ firebase_firestore.js?v=7f10f1b8:12731
z_ @ firebase_firestore.js?v=7f10f1b8:13003
W_ @ firebase_firestore.js?v=7f10f1b8:12967
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12957
Promise.then
auth @ firebase_firestore.js?v=7f10f1b8:12953
start @ firebase_firestore.js?v=7f10f1b8:12852
__PRIVATE_startWatchStream @ firebase_firestore.js?v=7f10f1b8:13310
__PRIVATE_remoteStoreListen @ firebase_firestore.js?v=7f10f1b8:13282
__PRIVATE_allocateTargetAndMaybeListen @ firebase_firestore.js?v=7f10f1b8:14345
await in __PRIVATE_allocateTargetAndMaybeListen
__PRIVATE_syncEngineListen @ firebase_firestore.js?v=7f10f1b8:14323
__PRIVATE_eventManagerListen @ firebase_firestore.js?v=7f10f1b8:13790
(anonymous) @ firebase_firestore.js?v=7f10f1b8:17989
await in (anonymous)
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
__PRIVATE_firestoreClientListen @ firebase_firestore.js?v=7f10f1b8:17989
onSnapshot @ firebase_firestore.js?v=7f10f1b8:17992
getUserNotifications @ notificationService.ts:226
(anonymous) @ useNotifications.ts:105
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9960
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9940
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9899
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11302
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<NotificationBadge>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
Header @ App.tsx:98
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopConcurrentByScheduler @ react-dom_client.js?v=7f10f1b8:10864
renderRootConcurrent @ react-dom_client.js?v=7f10f1b8:10844
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<Header>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
ProtectedLayout @ App.tsx:143
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopConcurrentByScheduler @ react-dom_client.js?v=7f10f1b8:10864
renderRootConcurrent @ react-dom_client.js?v=7f10f1b8:10844
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:109 ❌ Erreur lors de l'archivage: Missing or insufficient permissions.
overrideMethod @ hook.js:608
archiveYearData @ archiveService.ts:109
await in archiveYearData
checkAutoArchive @ archiveService.ts:229
await in checkAutoArchive
(anonymous) @ useArchive.ts:89
(anonymous) @ useArchive.ts:147
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9960
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9940
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9899
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11302
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
useArchive.ts:97 ❌ Erreur lors de la vérification d'archivage automatique: Échec de l'archivage des données 2024: Missing or insufficient permissions.
overrideMethod @ hook.js:608
(anonymous) @ useArchive.ts:97
await in (anonymous)
(anonymous) @ useArchive.ts:147
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9960
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9940
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9899
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11302
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:100  POST https://firestore.googleapis.com/google.firestore.v1.Firestore/Write/channel?VER=8&database=projects%2Fflorasynth-a461d%2Fdatabases%2F(default)&gsessionid=zD-vTDSZnJ-WT9NVCqPRcu5pJsJoK8WCQllQoduIS7o&SID=NlCOrKnnKE2bmMznnekkXw&RID=40711&TYPE=terminate&zx=7s3zsak6dlv7 400 (Bad Request)
gc @ firebase_firestore.js?v=7f10f1b8:2147
Y2.close @ firebase_firestore.js?v=7f10f1b8:2491
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12726
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12690
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Z2.ta @ firebase_firestore.js?v=7f10f1b8:2540
Rb @ firebase_firestore.js?v=7f10f1b8:1419
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1208
fd @ firebase_firestore.js?v=7f10f1b8:2341
h.Fa @ firebase_firestore.js?v=7f10f1b8:2308
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
ec @ firebase_firestore.js?v=7f10f1b8:2294
Rb @ firebase_firestore.js?v=7f10f1b8:1416
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1203
Hb @ firebase_firestore.js?v=7f10f1b8:1178
h.Ga @ firebase_firestore.js?v=7f10f1b8:2228
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
fc @ firebase_firestore.js?v=7f10f1b8:2172
h.connect @ firebase_firestore.js?v=7f10f1b8:2132
Y2.m @ firebase_firestore.js?v=7f10f1b8:2488
Ho @ firebase_firestore.js?v=7f10f1b8:12684
send @ firebase_firestore.js?v=7f10f1b8:12575
k_ @ firebase_firestore.js?v=7f10f1b8:12892
na @ firebase_firestore.js?v=7f10f1b8:13095
__PRIVATE_onWriteStreamOpen @ firebase_firestore.js?v=7f10f1b8:13447
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
o_ @ firebase_firestore.js?v=7f10f1b8:12581
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12732
setTimeout
P_ @ firebase_firestore.js?v=7f10f1b8:12731
z_ @ firebase_firestore.js?v=7f10f1b8:13074
W_ @ firebase_firestore.js?v=7f10f1b8:12967
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12957
Promise.then
auth @ firebase_firestore.js?v=7f10f1b8:12953
start @ firebase_firestore.js?v=7f10f1b8:12852
start @ firebase_firestore.js?v=7f10f1b8:13068
__PRIVATE_startWriteStream @ firebase_firestore.js?v=7f10f1b8:13444
__PRIVATE_fillWritePipeline @ firebase_firestore.js?v=7f10f1b8:13430
await in __PRIVATE_fillWritePipeline
__PRIVATE_syncEngineWrite @ firebase_firestore.js?v=7f10f1b8:14446
await in __PRIVATE_syncEngineWrite
(anonymous) @ firebase_firestore.js?v=7f10f1b8:18100
await in (anonymous)
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
__PRIVATE_firestoreClientWrite @ firebase_firestore.js?v=7f10f1b8:18100
executeWrite @ firebase_firestore.js?v=7f10f1b8:18101
setDoc @ firebase_firestore.js?v=7f10f1b8:17933
archiveYearData @ archiveService.ts:100
await in archiveYearData
checkAutoArchive @ archiveService.ts:229
await in checkAutoArchive
(anonymous) @ useArchive.ts:89
(anonymous) @ useArchive.ts:147
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9960
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9940
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9899
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11302
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:109 ❌ Erreur lors de l'archivage: Missing or insufficient permissions.
overrideMethod @ hook.js:608
archiveYearData @ archiveService.ts:109
await in archiveYearData
checkAutoArchive @ archiveService.ts:229
await in checkAutoArchive
(anonymous) @ useArchive.ts:89
(anonymous) @ App.tsx:129
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9960
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9940
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9899
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11302
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
useArchive.ts:97 ❌ Erreur lors de la vérification d'archivage automatique: Échec de l'archivage des données 2024: Missing or insufficient permissions.
overrideMethod @ hook.js:608
(anonymous) @ useArchive.ts:97
await in (anonymous)
(anonymous) @ App.tsx:129
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9960
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9940
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9899
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11302
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:100  POST https://firestore.googleapis.com/google.firestore.v1.Firestore/Write/channel?VER=8&database=projects%2Fflorasynth-a461d%2Fdatabases%2F(default)&gsessionid=OmhY3zspBjiJ5ZNeRQzEz0ACT64HZ2RQkTS7qBUf98E&SID=ox5gePXV3KKAtD6-AVuuTA&RID=82586&TYPE=terminate&zx=5p64e7br4kaw 400 (Bad Request)
gc @ firebase_firestore.js?v=7f10f1b8:2147
Y2.close @ firebase_firestore.js?v=7f10f1b8:2491
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12726
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12690
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Z2.ta @ firebase_firestore.js?v=7f10f1b8:2540
Rb @ firebase_firestore.js?v=7f10f1b8:1419
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1208
fd @ firebase_firestore.js?v=7f10f1b8:2341
h.Fa @ firebase_firestore.js?v=7f10f1b8:2308
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
ec @ firebase_firestore.js?v=7f10f1b8:2294
Rb @ firebase_firestore.js?v=7f10f1b8:1416
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1203
Hb @ firebase_firestore.js?v=7f10f1b8:1178
h.Ga @ firebase_firestore.js?v=7f10f1b8:2228
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
fc @ firebase_firestore.js?v=7f10f1b8:2172
h.connect @ firebase_firestore.js?v=7f10f1b8:2132
Y2.m @ firebase_firestore.js?v=7f10f1b8:2488
Ho @ firebase_firestore.js?v=7f10f1b8:12684
send @ firebase_firestore.js?v=7f10f1b8:12575
k_ @ firebase_firestore.js?v=7f10f1b8:12892
na @ firebase_firestore.js?v=7f10f1b8:13095
__PRIVATE_onWriteStreamOpen @ firebase_firestore.js?v=7f10f1b8:13447
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
o_ @ firebase_firestore.js?v=7f10f1b8:12581
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12732
setTimeout
P_ @ firebase_firestore.js?v=7f10f1b8:12731
z_ @ firebase_firestore.js?v=7f10f1b8:13074
W_ @ firebase_firestore.js?v=7f10f1b8:12967
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12957
Promise.then
auth @ firebase_firestore.js?v=7f10f1b8:12953
start @ firebase_firestore.js?v=7f10f1b8:12852
start @ firebase_firestore.js?v=7f10f1b8:13068
__PRIVATE_startWriteStream @ firebase_firestore.js?v=7f10f1b8:13444
__PRIVATE_fillWritePipeline @ firebase_firestore.js?v=7f10f1b8:13430
await in __PRIVATE_fillWritePipeline
__PRIVATE_handleWriteError @ firebase_firestore.js?v=7f10f1b8:13468
await in __PRIVATE_handleWriteError
__PRIVATE_onWriteStreamClose @ firebase_firestore.js?v=7f10f1b8:13470
close @ firebase_firestore.js?v=7f10f1b8:12942
G_ @ firebase_firestore.js?v=7f10f1b8:12984
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12972
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12972
__ @ firebase_firestore.js?v=7f10f1b8:12584
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12726
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12690
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Z2.ta @ firebase_firestore.js?v=7f10f1b8:2540
Rb @ firebase_firestore.js?v=7f10f1b8:1419
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1208
fd @ firebase_firestore.js?v=7f10f1b8:2341
h.Fa @ firebase_firestore.js?v=7f10f1b8:2308
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
ec @ firebase_firestore.js?v=7f10f1b8:2294
Rb @ firebase_firestore.js?v=7f10f1b8:1416
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1203
Hb @ firebase_firestore.js?v=7f10f1b8:1178
h.Ga @ firebase_firestore.js?v=7f10f1b8:2228
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
fc @ firebase_firestore.js?v=7f10f1b8:2172
h.connect @ firebase_firestore.js?v=7f10f1b8:2132
Y2.m @ firebase_firestore.js?v=7f10f1b8:2488
Ho @ firebase_firestore.js?v=7f10f1b8:12684
send @ firebase_firestore.js?v=7f10f1b8:12575
k_ @ firebase_firestore.js?v=7f10f1b8:12892
na @ firebase_firestore.js?v=7f10f1b8:13095
__PRIVATE_onWriteStreamOpen @ firebase_firestore.js?v=7f10f1b8:13447
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
o_ @ firebase_firestore.js?v=7f10f1b8:12581
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12732
setTimeout
P_ @ firebase_firestore.js?v=7f10f1b8:12731
z_ @ firebase_firestore.js?v=7f10f1b8:13074
W_ @ firebase_firestore.js?v=7f10f1b8:12967
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12957
Promise.then
auth @ firebase_firestore.js?v=7f10f1b8:12953
start @ firebase_firestore.js?v=7f10f1b8:12852
start @ firebase_firestore.js?v=7f10f1b8:13068
__PRIVATE_startWriteStream @ firebase_firestore.js?v=7f10f1b8:13444
__PRIVATE_fillWritePipeline @ firebase_firestore.js?v=7f10f1b8:13430
await in __PRIVATE_fillWritePipeline
__PRIVATE_syncEngineWrite @ firebase_firestore.js?v=7f10f1b8:14446
await in __PRIVATE_syncEngineWrite
(anonymous) @ firebase_firestore.js?v=7f10f1b8:18100
await in (anonymous)
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
__PRIVATE_firestoreClientWrite @ firebase_firestore.js?v=7f10f1b8:18100
executeWrite @ firebase_firestore.js?v=7f10f1b8:18101
setDoc @ firebase_firestore.js?v=7f10f1b8:17933
archiveYearData @ archiveService.ts:100
await in archiveYearData
checkAutoArchive @ archiveService.ts:229
await in checkAutoArchive
(anonymous) @ useArchive.ts:89
(anonymous) @ useArchive.ts:147
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9887
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9960
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9940
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9890
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9881
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9984
recursivelyTraversePassiveMountEffects @ react-dom_client.js?v=7f10f1b8:9868
commitPassiveMountOnFiber @ react-dom_client.js?v=7f10f1b8:9899
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11302
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
archiveService.ts:109 ❌ Erreur lors de l'archivage: Missing or insufficient permissions.
overrideMethod @ hook.js:608
archiveYearData @ archiveService.ts:109
await in archiveYearData
checkAutoArchive @ archiveService.ts:229
await in checkAutoArchive
(anonymous) @ useArchive.ts:89
(anonymous) @ useArchive.ts:147
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10016
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10054
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10031
doubleInvokeEffectsOnFiber @ react-dom_client.js?v=7f10f1b8:11461
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1487
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11442
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
commitDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11469
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11309
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
useArchive.ts:97 ❌ Erreur lors de la vérification d'archivage automatique: Échec de l'archivage des données 2024: Missing or insufficient permissions.
overrideMethod @ hook.js:608
(anonymous) @ useArchive.ts:97
await in (anonymous)
(anonymous) @ useArchive.ts:147
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10016
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10054
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10031
doubleInvokeEffectsOnFiber @ react-dom_client.js?v=7f10f1b8:11461
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1487
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11442
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
commitDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11469
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11309
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
firebase_firestore.js?v=7f10f1b8:2147  POST https://firestore.googleapis.com/google.firestore.v1.Firestore/Write/channel?VER=8&database=projects%2Fflorasynth-a461d%2Fdatabases%2F(default)&gsessionid=oMV9nhFzJ44QgHp7sxHTaMXPIBl0tl6gmyR6WLkZAl0&SID=J8HkU4Ufr3S2_PJzyM7Tpw&RID=67729&TYPE=terminate&zx=xzag59t2132 400 (Bad Request)
gc @ firebase_firestore.js?v=7f10f1b8:2147
Y2.close @ firebase_firestore.js?v=7f10f1b8:2491
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12726
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12690
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Z2.ta @ firebase_firestore.js?v=7f10f1b8:2540
Rb @ firebase_firestore.js?v=7f10f1b8:1419
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1208
fd @ firebase_firestore.js?v=7f10f1b8:2341
h.Fa @ firebase_firestore.js?v=7f10f1b8:2308
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
ec @ firebase_firestore.js?v=7f10f1b8:2294
Rb @ firebase_firestore.js?v=7f10f1b8:1416
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1203
Hb @ firebase_firestore.js?v=7f10f1b8:1178
h.Ga @ firebase_firestore.js?v=7f10f1b8:2228
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
fc @ firebase_firestore.js?v=7f10f1b8:2172
h.connect @ firebase_firestore.js?v=7f10f1b8:2132
Y2.m @ firebase_firestore.js?v=7f10f1b8:2488
Ho @ firebase_firestore.js?v=7f10f1b8:12684
send @ firebase_firestore.js?v=7f10f1b8:12575
k_ @ firebase_firestore.js?v=7f10f1b8:12892
na @ firebase_firestore.js?v=7f10f1b8:13095
__PRIVATE_onWriteStreamOpen @ firebase_firestore.js?v=7f10f1b8:13447
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
o_ @ firebase_firestore.js?v=7f10f1b8:12581
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12732
setTimeout
P_ @ firebase_firestore.js?v=7f10f1b8:12731
z_ @ firebase_firestore.js?v=7f10f1b8:13074
W_ @ firebase_firestore.js?v=7f10f1b8:12967
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12957
Promise.then
auth @ firebase_firestore.js?v=7f10f1b8:12953
start @ firebase_firestore.js?v=7f10f1b8:12852
start @ firebase_firestore.js?v=7f10f1b8:13068
__PRIVATE_startWriteStream @ firebase_firestore.js?v=7f10f1b8:13444
__PRIVATE_fillWritePipeline @ firebase_firestore.js?v=7f10f1b8:13430
await in __PRIVATE_fillWritePipeline
__PRIVATE_handleWriteError @ firebase_firestore.js?v=7f10f1b8:13468
await in __PRIVATE_handleWriteError
__PRIVATE_onWriteStreamClose @ firebase_firestore.js?v=7f10f1b8:13470
close @ firebase_firestore.js?v=7f10f1b8:12942
G_ @ firebase_firestore.js?v=7f10f1b8:12984
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12972
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12972
__ @ firebase_firestore.js?v=7f10f1b8:12584
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12726
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12690
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Z2.ta @ firebase_firestore.js?v=7f10f1b8:2540
Rb @ firebase_firestore.js?v=7f10f1b8:1419
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1208
fd @ firebase_firestore.js?v=7f10f1b8:2341
h.Fa @ firebase_firestore.js?v=7f10f1b8:2308
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
ec @ firebase_firestore.js?v=7f10f1b8:2294
Rb @ firebase_firestore.js?v=7f10f1b8:1416
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1203
Hb @ firebase_firestore.js?v=7f10f1b8:1178
h.Ga @ firebase_firestore.js?v=7f10f1b8:2228
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
fc @ firebase_firestore.js?v=7f10f1b8:2172
h.connect @ firebase_firestore.js?v=7f10f1b8:2132
Y2.m @ firebase_firestore.js?v=7f10f1b8:2488
Ho @ firebase_firestore.js?v=7f10f1b8:12684
send @ firebase_firestore.js?v=7f10f1b8:12575
k_ @ firebase_firestore.js?v=7f10f1b8:12892
na @ firebase_firestore.js?v=7f10f1b8:13095
__PRIVATE_onWriteStreamOpen @ firebase_firestore.js?v=7f10f1b8:13447
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
o_ @ firebase_firestore.js?v=7f10f1b8:12581
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12732
setTimeout
P_ @ firebase_firestore.js?v=7f10f1b8:12731
z_ @ firebase_firestore.js?v=7f10f1b8:13074
W_ @ firebase_firestore.js?v=7f10f1b8:12967
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12957
Promise.then
auth @ firebase_firestore.js?v=7f10f1b8:12953
start @ firebase_firestore.js?v=7f10f1b8:12852
start @ firebase_firestore.js?v=7f10f1b8:13068
__PRIVATE_startWriteStream @ firebase_firestore.js?v=7f10f1b8:13444
__PRIVATE_fillWritePipeline @ firebase_firestore.js?v=7f10f1b8:13430
await in __PRIVATE_fillWritePipeline
__PRIVATE_handleWriteError @ firebase_firestore.js?v=7f10f1b8:13468
await in __PRIVATE_handleWriteError
__PRIVATE_onWriteStreamClose @ firebase_firestore.js?v=7f10f1b8:13470
close @ firebase_firestore.js?v=7f10f1b8:12942
G_ @ firebase_firestore.js?v=7f10f1b8:12984
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12972
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12972
__ @ firebase_firestore.js?v=7f10f1b8:12584
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12726
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12690
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Z2.ta @ firebase_firestore.js?v=7f10f1b8:2540
Rb @ firebase_firestore.js?v=7f10f1b8:1419
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1208
fd @ firebase_firestore.js?v=7f10f1b8:2341
h.Fa @ firebase_firestore.js?v=7f10f1b8:2308
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
ec @ firebase_firestore.js?v=7f10f1b8:2294
Rb @ firebase_firestore.js?v=7f10f1b8:1416
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1203
Hb @ firebase_firestore.js?v=7f10f1b8:1178
h.Ga @ firebase_firestore.js?v=7f10f1b8:2228
Da @ firebase_firestore.js?v=7f10f1b8:669Understand this error
archiveService.ts:109 ❌ Erreur lors de l'archivage: Missing or insufficient permissions.
overrideMethod @ hook.js:608
archiveYearData @ archiveService.ts:109
await in archiveYearData
checkAutoArchive @ archiveService.ts:229
await in checkAutoArchive
(anonymous) @ useArchive.ts:89
(anonymous) @ App.tsx:129
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10016
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10054
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10031
doubleInvokeEffectsOnFiber @ react-dom_client.js?v=7f10f1b8:11461
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1487
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11442
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
commitDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11469
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11309
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
useArchive.ts:97 ❌ Erreur lors de la vérification d'archivage automatique: Échec de l'archivage des données 2024: Missing or insufficient permissions.
overrideMethod @ hook.js:608
(anonymous) @ useArchive.ts:97
await in (anonymous)
(anonymous) @ App.tsx:129
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17478
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
commitHookEffectListMount @ react-dom_client.js?v=7f10f1b8:8460
commitHookPassiveMountEffects @ react-dom_client.js?v=7f10f1b8:8518
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10016
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10054
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10009
recursivelyTraverseReconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:9995
reconnectPassiveEffects @ react-dom_client.js?v=7f10f1b8:10031
doubleInvokeEffectsOnFiber @ react-dom_client.js?v=7f10f1b8:11461
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1487
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11442
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
recursivelyTraverseAndDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11438
commitDoubleInvokeEffectsInDEV @ react-dom_client.js?v=7f10f1b8:11469
flushPassiveEffects @ react-dom_client.js?v=7f10f1b8:11309
flushPendingEffects @ react-dom_client.js?v=7f10f1b8:11276
performSyncWorkOnRoot @ react-dom_client.js?v=7f10f1b8:11632
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=7f10f1b8:11536
flushSpawnedWork @ react-dom_client.js?v=7f10f1b8:11254
commitRoot @ react-dom_client.js?v=7f10f1b8:11081
commitRootWhenReady @ react-dom_client.js?v=7f10f1b8:10512
<ProtectedLayout>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
App @ App.tsx:161
react-stack-bottom-frame @ react-dom_client.js?v=7f10f1b8:17424
renderWithHooksAgain @ react-dom_client.js?v=7f10f1b8:4281
renderWithHooks @ react-dom_client.js?v=7f10f1b8:4217
updateFunctionComponent @ react-dom_client.js?v=7f10f1b8:6619
beginWork @ react-dom_client.js?v=7f10f1b8:7654
runWithFiberInDEV @ react-dom_client.js?v=7f10f1b8:1485
performUnitOfWork @ react-dom_client.js?v=7f10f1b8:10868
workLoopSync @ react-dom_client.js?v=7f10f1b8:10728
renderRootSync @ react-dom_client.js?v=7f10f1b8:10711
performWorkOnRoot @ react-dom_client.js?v=7f10f1b8:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=7f10f1b8:11623
performWorkUntilDeadline @ react-dom_client.js?v=7f10f1b8:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=7f10f1b8:250
(anonymous) @ index.tsx:14Understand this error
firebase_firestore.js?v=7f10f1b8:2147  POST https://firestore.googleapis.com/google.firestore.v1.Firestore/Write/channel?VER=8&database=projects%2Fflorasynth-a461d%2Fdatabases%2F(default)&gsessionid=Tru_RJyAaiCxulRElIh1qkPo3Wh7Oua3eFeEoD9mz1g&SID=gvrbyK63OCv5FlDzLwl-Yw&RID=42511&TYPE=terminate&zx=kex61c9rcw1e 400 (Bad Request)
gc @ firebase_firestore.js?v=7f10f1b8:2147
Y2.close @ firebase_firestore.js?v=7f10f1b8:2491
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12726
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12690
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Z2.ta @ firebase_firestore.js?v=7f10f1b8:2540
Rb @ firebase_firestore.js?v=7f10f1b8:1419
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1208
fd @ firebase_firestore.js?v=7f10f1b8:2341
h.Fa @ firebase_firestore.js?v=7f10f1b8:2308
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
ec @ firebase_firestore.js?v=7f10f1b8:2294
Rb @ firebase_firestore.js?v=7f10f1b8:1416
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1203
Hb @ firebase_firestore.js?v=7f10f1b8:1178
h.Ga @ firebase_firestore.js?v=7f10f1b8:2228
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
fc @ firebase_firestore.js?v=7f10f1b8:2172
h.connect @ firebase_firestore.js?v=7f10f1b8:2132
Y2.m @ firebase_firestore.js?v=7f10f1b8:2488
Ho @ firebase_firestore.js?v=7f10f1b8:12684
send @ firebase_firestore.js?v=7f10f1b8:12575
k_ @ firebase_firestore.js?v=7f10f1b8:12892
na @ firebase_firestore.js?v=7f10f1b8:13095
__PRIVATE_onWriteStreamOpen @ firebase_firestore.js?v=7f10f1b8:13447
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
o_ @ firebase_firestore.js?v=7f10f1b8:12581
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12732
setTimeout
P_ @ firebase_firestore.js?v=7f10f1b8:12731
z_ @ firebase_firestore.js?v=7f10f1b8:13074
W_ @ firebase_firestore.js?v=7f10f1b8:12967
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12957
Promise.then
auth @ firebase_firestore.js?v=7f10f1b8:12953
start @ firebase_firestore.js?v=7f10f1b8:12852
start @ firebase_firestore.js?v=7f10f1b8:13068
__PRIVATE_startWriteStream @ firebase_firestore.js?v=7f10f1b8:13444
__PRIVATE_fillWritePipeline @ firebase_firestore.js?v=7f10f1b8:13430
await in __PRIVATE_fillWritePipeline
__PRIVATE_handleWriteError @ firebase_firestore.js?v=7f10f1b8:13468
await in __PRIVATE_handleWriteError
__PRIVATE_onWriteStreamClose @ firebase_firestore.js?v=7f10f1b8:13470
close @ firebase_firestore.js?v=7f10f1b8:12942
G_ @ firebase_firestore.js?v=7f10f1b8:12984
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12972
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12972
__ @ firebase_firestore.js?v=7f10f1b8:12584
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12726
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12690
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Z2.ta @ firebase_firestore.js?v=7f10f1b8:2540
Rb @ firebase_firestore.js?v=7f10f1b8:1419
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1208
fd @ firebase_firestore.js?v=7f10f1b8:2341
h.Fa @ firebase_firestore.js?v=7f10f1b8:2308
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
ec @ firebase_firestore.js?v=7f10f1b8:2294
Rb @ firebase_firestore.js?v=7f10f1b8:1416
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1203
Hb @ firebase_firestore.js?v=7f10f1b8:1178
h.Ga @ firebase_firestore.js?v=7f10f1b8:2228
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
fc @ firebase_firestore.js?v=7f10f1b8:2172
h.connect @ firebase_firestore.js?v=7f10f1b8:2132
Y2.m @ firebase_firestore.js?v=7f10f1b8:2488
Ho @ firebase_firestore.js?v=7f10f1b8:12684
send @ firebase_firestore.js?v=7f10f1b8:12575
k_ @ firebase_firestore.js?v=7f10f1b8:12892
na @ firebase_firestore.js?v=7f10f1b8:13095
__PRIVATE_onWriteStreamOpen @ firebase_firestore.js?v=7f10f1b8:13447
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12970
o_ @ firebase_firestore.js?v=7f10f1b8:12581
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12732
setTimeout
P_ @ firebase_firestore.js?v=7f10f1b8:12731
z_ @ firebase_firestore.js?v=7f10f1b8:13074
W_ @ firebase_firestore.js?v=7f10f1b8:12967
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12957
Promise.then
auth @ firebase_firestore.js?v=7f10f1b8:12953
start @ firebase_firestore.js?v=7f10f1b8:12852
start @ firebase_firestore.js?v=7f10f1b8:13068
__PRIVATE_startWriteStream @ firebase_firestore.js?v=7f10f1b8:13444
__PRIVATE_fillWritePipeline @ firebase_firestore.js?v=7f10f1b8:13430
await in __PRIVATE_fillWritePipeline
__PRIVATE_handleWriteError @ firebase_firestore.js?v=7f10f1b8:13468
await in __PRIVATE_handleWriteError
__PRIVATE_onWriteStreamClose @ firebase_firestore.js?v=7f10f1b8:13470
close @ firebase_firestore.js?v=7f10f1b8:12942
G_ @ firebase_firestore.js?v=7f10f1b8:12984
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12972
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16086
(anonymous) @ firebase_firestore.js?v=7f10f1b8:16117
Promise.then
uc @ firebase_firestore.js?v=7f10f1b8:16117
enqueue @ firebase_firestore.js?v=7f10f1b8:16086
enqueueAndForget @ firebase_firestore.js?v=7f10f1b8:16068
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12994
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12972
__ @ firebase_firestore.js?v=7f10f1b8:12584
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12726
(anonymous) @ firebase_firestore.js?v=7f10f1b8:12690
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Z2.ta @ firebase_firestore.js?v=7f10f1b8:2540
Rb @ firebase_firestore.js?v=7f10f1b8:1419
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Pa @ firebase_firestore.js?v=7f10f1b8:1814
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1208
fd @ firebase_firestore.js?v=7f10f1b8:2341
h.Fa @ firebase_firestore.js?v=7f10f1b8:2308
Da @ firebase_firestore.js?v=7f10f1b8:669
Promise.then
x2 @ firebase_firestore.js?v=7f10f1b8:663
ec @ firebase_firestore.js?v=7f10f1b8:2294
Rb @ firebase_firestore.js?v=7f10f1b8:1416
M2.Y @ firebase_firestore.js?v=7f10f1b8:1284
M2.ca @ firebase_firestore.js?v=7f10f1b8:1215
ab @ firebase_firestore.js?v=7f10f1b8:950
F2 @ firebase_firestore.js?v=7f10f1b8:920
Wc @ firebase_firestore.js?v=7f10f1b8:1954
h.bb @ firebase_firestore.js?v=7f10f1b8:1949
h.Ea @ firebase_firestore.js?v=7f10f1b8:1946
Lc @ firebase_firestore.js?v=7f10f1b8:1846
h.Pa @ firebase_firestore.js?v=7f10f1b8:1813
Promise.then
Nc @ firebase_firestore.js?v=7f10f1b8:1804
h.Sa @ firebase_firestore.js?v=7f10f1b8:1800
Promise.then
h.send @ firebase_firestore.js?v=7f10f1b8:1781
h.ea @ firebase_firestore.js?v=7f10f1b8:1922
Jb @ firebase_firestore.js?v=7f10f1b8:1203
Hb @ firebase_firestore.js?v=7f10f1b8:1178
h.Ga @ firebase_firestore.js?v=7f10f1b8:2228
Da @ firebase_firestore.js?v=7f10f1b8:669Understand this error
